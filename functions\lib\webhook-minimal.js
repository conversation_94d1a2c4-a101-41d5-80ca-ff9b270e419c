"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.testMinimalWebhook = exports.stripeWebhookMinimal = void 0;
// Minimal webhook functions for deployment
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}
// Helper function to generate 6-digit secret code
function generateSecretCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}
// Minimal Stripe webhook handler
exports.stripeWebhookMinimal = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 60,
})
    .https.onRequest(async (req, res) => {
    try {
        console.log('🔗 Minimal webhook received');
        // Basic webhook signature verification would go here
        // For now, we'll process the event directly
        const event = req.body;
        console.log(`📨 Processing webhook event: ${event.type}`);
        // Handle checkout session completed
        if (event.type === 'checkout.session.completed') {
            const session = event.data.object;
            const metadata = session.metadata;
            if (metadata && metadata.orderId) {
                const orderId = metadata.orderId;
                console.log(`📦 Processing order: ${orderId}`);
                // Get the order from Firestore
                const orderRef = admin.firestore().collection('orders').doc(orderId);
                const orderDoc = await orderRef.get();
                if (orderDoc.exists) {
                    const orderData = orderDoc.data();
                    // Generate 6-digit secret code
                    const secretCode = generateSecretCode();
                    console.log(`🔐 Generated secret code: ${secretCode} for order: ${orderId}`);
                    // Update order with payment completion and secret code
                    await orderRef.update({
                        status: 'payment_completed',
                        stripeSessionId: session.id,
                        stripePaymentIntentId: session.payment_intent,
                        secretCode: secretCode,
                        paymentCompletedAt: admin.firestore.Timestamp.now(),
                        updatedAt: admin.firestore.Timestamp.now()
                    });
                    console.log(`✅ Order ${orderId} updated with payment completion and secret code`);
                    // Update listing status to sold
                    if (orderData && orderData.listingId) {
                        await admin.firestore().collection('listings').doc(orderData.listingId).update({
                            status: 'sold',
                            soldAt: admin.firestore.Timestamp.now(),
                            updatedAt: admin.firestore.Timestamp.now()
                        });
                        console.log(`✅ Listing ${orderData.listingId} marked as sold`);
                    }
                }
            }
        }
        // Handle payment intent succeeded
        if (event.type === 'payment_intent.succeeded') {
            const paymentIntent = event.data.object;
            const metadata = paymentIntent.metadata;
            if (metadata && metadata.orderId) {
                const orderId = metadata.orderId;
                console.log(`💰 Processing payment for order: ${orderId}`);
                // Get the order from Firestore
                const orderRef = admin.firestore().collection('orders').doc(orderId);
                const orderDoc = await orderRef.get();
                if (orderDoc.exists) {
                    // Update order with payment success
                    await orderRef.update({
                        status: 'in_progress',
                        stripePaymentIntentId: paymentIntent.id,
                        paymentSucceededAt: admin.firestore.Timestamp.now(),
                        updatedAt: admin.firestore.Timestamp.now()
                    });
                    // Update listing status to sold
                    if (metadata.listingId) {
                        await admin.firestore().collection('listings').doc(metadata.listingId).update({
                            status: 'sold',
                            soldAt: admin.firestore.Timestamp.now(),
                            updatedAt: admin.firestore.Timestamp.now()
                        });
                        console.log(`✅ Listing ${metadata.listingId} marked as sold`);
                    }
                }
            }
        }
        console.log('✅ Minimal webhook processed successfully');
        res.status(200).json({ received: true, processed: true });
    }
    catch (error) {
        console.error('❌ Error processing minimal webhook:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown webhook processing error';
        res.status(500).send(`Webhook processing failed: ${errorMessage}`);
    }
});
// Test function to verify deployment
exports.testMinimalWebhook = functions
    .runWith({
    memory: '128MB',
    timeoutSeconds: 10
})
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Minimal webhook functions working',
        timestamp: new Date().toISOString(),
        secretCodeTest: generateSecretCode()
    });
});
