"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requestReturn = exports.markDeliveryCompleted = exports.autoReleaseEscrow = exports.releaseEscrowWithCode = exports.createCheckoutSession = exports.stripeApi = exports.stripeWebhook = exports.configureWalletSettings = exports.getWalletSettingsHttp = exports.getAdminWalletData = exports.processReferralCode = exports.grantWalletCredit = exports.configureWalletSettingsCallable = exports.getWalletSettings = exports.getWalletData = exports.getStripeConnectOnboardingLink = exports.createStripeConnectAccount = exports.getSellerPendingPayouts = exports.getStripeConnectAccountStatus = exports.fixAdminUser = exports.verifyAdminPin = exports.setAdminPin = exports.testFunction = void 0;
// Firebase Functions for Hive Campus
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
const crypto_1 = require("crypto");
const cors_1 = __importDefault(require("cors"));
const Stripe = require('stripe');
// Wallet utility imports removed - functions implemented inline
// Initialize Firebase Admin
admin.initializeApp();
// CORS configuration for development and production
const corsHandler = (0, cors_1.default)({
    origin: [
        'https://h1c1-798a8.web.app',
        'https://h1c1-798a8.firebaseapp.com',
        'https://hivecampus.app',
        'https://www.hivecampus.app',
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:3000',
        'http://localhost:5000'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});
// Simple test function
exports.testFunction = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onRequest(async (_req, res) => {
    res.json({
        success: true,
        message: 'Test function working',
        timestamp: new Date().toISOString()
    });
});
// Function to set admin PIN
exports.setAdminPin = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Verify the user is authenticated and is an admin
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { pin } = data;
        if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
            throw new functions.https.HttpsError('invalid-argument', 'PIN must be exactly 8 digits');
        }
        // Verify user is admin
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admin users can set PIN');
        }
        // Hash the PIN for security
        const hashedPin = (0, crypto_1.createHash)('sha256').update(pin).digest('hex');
        // Store the hashed PIN in admin settings
        await admin.firestore().collection('adminSettings').doc('security').set({
            adminPin: hashedPin,
            pinSetAt: admin.firestore.Timestamp.now(),
            pinSetBy: context.auth.uid
        }, { merge: true });
        console.log(`Admin PIN set by user: ${context.auth.uid}`);
        return {
            success: true,
            message: 'Admin PIN set successfully'
        };
    }
    catch (error) {
        console.error('Error setting admin PIN:', error);
        throw new functions.https.HttpsError('internal', 'Failed to set admin PIN', error);
    }
});
// Function to verify admin PIN
exports.verifyAdminPin = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, context) => {
    var _a, _b, _c;
    try {
        // Verify the user is authenticated and is an admin
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { pin } = data;
        if (!pin || pin.length !== 8 || !/^\d{8}$/.test(pin)) {
            throw new functions.https.HttpsError('invalid-argument', 'PIN must be exactly 8 digits');
        }
        // Verify user is admin
        const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!userDoc.exists || ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Only admin users can verify PIN');
        }
        // Get stored PIN hash
        const securityDoc = await admin.firestore().collection('adminSettings').doc('security').get();
        if (!securityDoc.exists || !((_b = securityDoc.data()) === null || _b === void 0 ? void 0 : _b.adminPin)) {
            throw new functions.https.HttpsError('not-found', 'Admin PIN not set. Please set up your PIN first.');
        }
        // Hash the provided PIN and compare
        const hashedPin = (0, crypto_1.createHash)('sha256').update(pin).digest('hex');
        const storedPin = (_c = securityDoc.data()) === null || _c === void 0 ? void 0 : _c.adminPin;
        if (hashedPin !== storedPin) {
            throw new functions.https.HttpsError('permission-denied', 'Invalid PIN');
        }
        // Update last access time
        await admin.firestore().collection('users').doc(context.auth.uid).update({
            lastAdminAccess: admin.firestore.Timestamp.now()
        });
        console.log(`Admin PIN verified for user: ${context.auth.uid}`);
        return {
            success: true,
            message: 'PIN verified successfully'
        };
    }
    catch (error) {
        console.error('Error verifying admin PIN:', error);
        // If it's already a Firebase error, re-throw it as-is
        if (error instanceof functions.https.HttpsError) {
            throw error;
        }
        // Otherwise, wrap it as an internal error
        throw new functions.https.HttpsError('internal', 'Failed to verify admin PIN', error);
    }
});
// Function to fix existing admin user (for setup)
exports.fixAdminUser = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60
})
    .https.onCall(async (data, _context) => {
    try {
        const { email } = data;
        if (!email) {
            throw new functions.https.HttpsError('invalid-argument', 'Email is required');
        }
        // Get user by email
        const userRecord = await admin.auth().getUserByEmail(email);
        // Set custom claims with both admin and role
        await admin.auth().setCustomUserClaims(userRecord.uid, {
            admin: true,
            role: 'admin'
        });
        // Update user profile in Firestore
        await admin.firestore().collection('users').doc(userRecord.uid).set({
            role: 'admin',
            updatedAt: admin.firestore.Timestamp.now(),
            status: 'active',
            adminLevel: 'super',
            permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
        }, { merge: true });
        console.log(`Admin user fixed: ${email} (${userRecord.uid})`);
        return {
            success: true,
            message: 'Admin user configured successfully',
            uid: userRecord.uid
        };
    }
    catch (error) {
        console.error('Error fixing admin user:', error);
        throw new functions.https.HttpsError('internal', 'Failed to fix admin user', error);
    }
});
// Get Stripe Connect account status
exports.getStripeConnectAccountStatus = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log('Fetching Stripe Connect account status for user:', userId);
        // Get connect account from Firestore
        const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (!connectAccountDoc.exists) {
            console.log(`No connect account found for user: ${userId}`);
            return null;
        }
        const connectAccount = connectAccountDoc.data();
        console.log('Connect account data:', connectAccount);
        return {
            accountId: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.stripeAccountId) || null,
            onboardingUrl: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.onboardingUrl) || null,
            dashboardUrl: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.dashboardUrl) || null,
            isOnboarded: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.isOnboarded) || false,
            chargesEnabled: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.chargesEnabled) || false,
            payoutsEnabled: (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.payoutsEnabled) || false,
        };
    }
    catch (error) {
        console.error('Error in getStripeConnectAccountStatus:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get pending payouts for seller
exports.getSellerPendingPayouts = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log('Fetching pending payouts for user:', userId);
        let pendingPayouts = [];
        try {
            // Primary query with composite index
            const ordersQuery = admin.firestore()
                .collection('orders')
                .where('sellerId', '==', userId)
                .where('status', '==', 'payment_succeeded')
                .where('fundsReleased', '==', false)
                .orderBy('createdAt', 'desc')
                .limit(50);
            const ordersSnapshot = await ordersQuery.get();
            pendingPayouts = ordersSnapshot.docs.map(doc => (Object.assign({ id: doc.id }, doc.data())));
        }
        catch (indexError) {
            console.warn('Primary query failed, trying fallback without orderBy:', indexError);
            // Fallback query without orderBy (requires fewer indexes)
            try {
                const fallbackQuery = admin.firestore()
                    .collection('orders')
                    .where('sellerId', '==', userId)
                    .where('status', '==', 'payment_succeeded')
                    .where('fundsReleased', '==', false)
                    .limit(50);
                const fallbackSnapshot = await fallbackQuery.get();
                pendingPayouts = fallbackSnapshot.docs
                    .map(doc => (Object.assign({ id: doc.id }, doc.data())))
                    .sort((a, b) => {
                    var _a, _b, _c, _d;
                    // Sort in memory if createdAt exists
                    const aTime = ((_b = (_a = a.createdAt) === null || _a === void 0 ? void 0 : _a.toMillis) === null || _b === void 0 ? void 0 : _b.call(_a)) || 0;
                    const bTime = ((_d = (_c = b.createdAt) === null || _c === void 0 ? void 0 : _c.toMillis) === null || _d === void 0 ? void 0 : _d.call(_c)) || 0;
                    return bTime - aTime;
                });
            }
            catch (fallbackError) {
                console.error('Fallback query also failed:', fallbackError);
                throw fallbackError;
            }
        }
        console.log(`Found ${pendingPayouts.length} pending payouts for user: ${userId}`);
        return pendingPayouts;
    }
    catch (error) {
        console.error('Error in getSellerPendingPayouts:', error);
        // Handle specific Firestore errors
        if (error instanceof Error) {
            if (error.message.includes('index')) {
                console.error('Missing Firestore index. Please deploy indexes with: firebase deploy --only firestore:indexes');
                throw new functions.https.HttpsError('failed-precondition', 'Database index required. Please contact support.');
            }
            if (error.message.includes('permission')) {
                throw new functions.https.HttpsError('permission-denied', 'Insufficient permissions to access pending payouts');
            }
            if (error.message.includes('quota')) {
                throw new functions.https.HttpsError('resource-exhausted', 'Service temporarily overloaded. Please try again later.');
            }
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Create Stripe Connect account
exports.createStripeConnectAccount = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { accountType } = data;
        const userId = context.auth.uid;
        if (!accountType || !['student', 'merchant'].includes(accountType)) {
            throw new functions.https.HttpsError('invalid-argument', 'Valid accountType is required');
        }
        console.log(`Creating Stripe Connect account for user: ${userId}, type: ${accountType}`);
        // Check if user already has a Connect account
        const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (existingAccountDoc.exists) {
            const existingAccount = existingAccountDoc.data();
            console.log('User already has a Connect account:', existingAccount);
            return {
                accountId: existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.stripeAccountId,
                onboardingUrl: (existingAccount === null || existingAccount === void 0 ? void 0 : existingAccount.onboardingUrl) || `https://hivecampus.app/settings/payment?existing=true`
            };
        }
        // For now, create a mock account since we don't have Stripe configured
        const mockAccountId = `acct_mock_${userId.substring(0, 8)}`;
        const mockConnectAccount = {
            userId: userId,
            stripeAccountId: mockAccountId,
            accountType: accountType,
            isOnboarded: false,
            chargesEnabled: false,
            payoutsEnabled: false,
            detailsSubmitted: false,
            onboardingUrl: `https://hivecampus.app/settings/payment?mock=true&message=Stripe Connect setup will be available soon`,
            isMockAccount: true,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
        };
        await admin.firestore().collection('connectAccounts').doc(userId).set(mockConnectAccount);
        console.log(`Mock Stripe Connect account created for user: ${userId}`);
        return {
            accountId: mockAccountId,
            onboardingUrl: mockConnectAccount.onboardingUrl,
        };
    }
    catch (error) {
        console.error('Error in createStripeConnectAccount:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get Stripe Connect onboarding link
exports.getStripeConnectOnboardingLink = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log(`Getting onboarding link for user: ${userId}`);
        // Get connect account from Firestore
        const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
        if (!connectAccountDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'No Stripe account found for user');
        }
        const connectAccount = connectAccountDoc.data();
        if (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.isOnboarded) {
            throw new functions.https.HttpsError('failed-precondition', 'Account is already fully onboarded');
        }
        // Return the existing onboarding URL or create a new mock one
        const onboardingUrl = (connectAccount === null || connectAccount === void 0 ? void 0 : connectAccount.onboardingUrl) ||
            `https://hivecampus.app/settings/payment?mock=true&message=Stripe Connect setup will be available soon`;
        return {
            onboardingUrl: onboardingUrl
        };
    }
    catch (error) {
        console.error('Error in getStripeConnectOnboardingLink:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get wallet data for a user
exports.getWalletData = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const userId = context.auth.uid;
        console.log('Getting wallet data for user:', userId);
        // Get wallet from Firestore
        const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();
        if (!walletDoc.exists) {
            // Initialize wallet if it doesn't exist
            const referralCode = `user${userId.substring(0, 6)}`;
            const walletData = {
                userId,
                balance: 0,
                referralCode,
                usedReferral: false,
                history: [],
                grantedBy: 'system',
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            };
            await admin.firestore().collection('wallets').doc(userId).set(walletData);
            return walletData;
        }
        return walletDoc.data();
    }
    catch (error) {
        console.error('Error in getWalletData:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get wallet settings (admin only)
exports.getWalletSettings = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        console.log('Getting wallet settings for admin:', context.auth.uid);
        // Get wallet settings from admin configuration
        const settingsDoc = await admin.firestore().collection('adminSettings').doc('walletConfig').get();
        let settings = {
            signupBonus: 0,
            referralBonus: 0,
            enableSignupBonus: false,
            enableReferralBonus: false
        };
        if (settingsDoc.exists) {
            const data = settingsDoc.data();
            settings = {
                signupBonus: (data === null || data === void 0 ? void 0 : data.signupBonus) || 0,
                referralBonus: (data === null || data === void 0 ? void 0 : data.referralBonus) || 0,
                enableSignupBonus: (data === null || data === void 0 ? void 0 : data.enableSignupBonus) || false,
                enableReferralBonus: (data === null || data === void 0 ? void 0 : data.enableReferralBonus) || false
            };
        }
        return {
            success: true,
            settings
        };
    }
    catch (error) {
        console.error('Error in getWalletSettings:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Configure wallet settings (admin only, callable function)
exports.configureWalletSettingsCallable = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        // Get settings from data
        const { signupBonus, referralBonus, enableSignupBonus, enableReferralBonus } = data;
        // Validate settings
        if (typeof signupBonus !== 'number' || typeof referralBonus !== 'number' ||
            typeof enableSignupBonus !== 'boolean' || typeof enableReferralBonus !== 'boolean') {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid settings format');
        }
        // Save settings
        await admin.firestore().collection('adminSettings').doc('walletConfig').set({
            signupBonus,
            referralBonus,
            enableSignupBonus,
            enableReferralBonus,
            updatedAt: admin.firestore.Timestamp.now(),
            updatedBy: context.auth.uid
        });
        console.log(`Wallet settings updated by admin: ${context.auth.uid}`);
        return {
            success: true,
            message: 'Wallet settings updated successfully'
        };
    }
    catch (error) {
        console.error('Error configuring wallet settings:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Grant wallet credit to a user (admin only) - Callable version
exports.grantWalletCredit = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const { userId, amount, description } = data;
        if (!userId || !amount || amount <= 0) {
            throw new functions.https.HttpsError('invalid-argument', 'Valid user ID and positive amount are required');
        }
        // Check if target user exists
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        if (!userDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'User not found');
        }
        console.log(`Admin ${context.auth.uid} granting $${amount} to user ${userId}`);
        // Create transaction record
        const transaction = {
            id: admin.firestore().collection('temp').doc().id,
            userId: userId,
            type: 'admin_grant',
            amount: parseFloat(amount.toFixed(2)),
            description: description || `Admin credit grant`,
            timestamp: admin.firestore.Timestamp.now(),
            grantedBy: context.auth.uid,
            source: 'admin_grant',
            createdAt: admin.firestore.Timestamp.now()
        };
        // Get or create wallet
        const walletRef = admin.firestore().collection('wallets').doc(userId);
        const walletDoc = await walletRef.get();
        if (!walletDoc.exists) {
            // Create new wallet
            const referralCode = `user${userId.substring(0, 6)}`;
            await walletRef.set({
                userId,
                balance: transaction.amount,
                referralCode,
                usedReferral: false,
                history: [transaction],
                grantedBy: 'admin',
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        else {
            // Update existing wallet
            await walletRef.update({
                balance: admin.firestore.FieldValue.increment(transaction.amount),
                history: admin.firestore.FieldValue.arrayUnion(transaction),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        return {
            success: true,
            message: `Successfully granted $${transaction.amount} to user`,
            transaction
        };
    }
    catch (error) {
        console.error('Error in grantWalletCredit:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Process referral code and grant bonuses
exports.processReferralCode = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a, _b;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { referralCode } = data;
        const userId = context.auth.uid;
        if (!referralCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Referral code is required');
        }
        console.log(`Processing referral code ${referralCode} for user ${userId}`);
        // Get wallet settings to check if referral bonuses are enabled
        const settingsDoc = await admin.firestore().collection('adminSettings').doc('walletConfig').get();
        const settings = settingsDoc.exists ? settingsDoc.data() : {
            enableReferralBonus: false,
            referralBonus: 5
        };
        if (!(settings === null || settings === void 0 ? void 0 : settings.enableReferralBonus)) {
            throw new functions.https.HttpsError('failed-precondition', 'Referral bonuses are currently disabled');
        }
        const referralBonus = (settings === null || settings === void 0 ? void 0 : settings.referralBonus) || 5;
        // Check if referral code exists
        const referralDoc = await admin.firestore().collection('referralCodes').doc(referralCode).get();
        if (!referralDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Invalid referral code');
        }
        const referralData = referralDoc.data();
        if (!referralData) {
            throw new functions.https.HttpsError('not-found', 'Invalid referral code data');
        }
        // Check if user is trying to use their own referral code
        if (referralData.userId === userId) {
            throw new functions.https.HttpsError('invalid-argument', 'Cannot use your own referral code');
        }
        // Check if user has already used a referral code
        const userWalletDoc = await admin.firestore().collection('wallets').doc(userId).get();
        if (userWalletDoc.exists && ((_a = userWalletDoc.data()) === null || _a === void 0 ? void 0 : _a.usedReferral)) {
            throw new functions.https.HttpsError('failed-precondition', 'You have already used a referral code');
        }
        // Check if this user has already been referred by this code
        if ((_b = referralData.usedBy) === null || _b === void 0 ? void 0 : _b.includes(userId)) {
            throw new functions.https.HttpsError('failed-precondition', 'You have already used this referral code');
        }
        // Create transactions for both users
        const now = admin.firestore.Timestamp.now();
        const newUserTransaction = {
            id: admin.firestore().collection('temp').doc().id,
            userId: userId,
            type: 'referral_bonus',
            amount: referralBonus,
            description: `Referral bonus for using code ${referralCode}`,
            timestamp: now,
            source: 'referral_bonus',
            createdAt: now
        };
        const referrerTransaction = {
            id: admin.firestore().collection('temp').doc().id,
            userId: referralData.userId,
            type: 'referral_bonus',
            amount: referralBonus,
            description: `Referral bonus for referring new user`,
            timestamp: now,
            source: 'referral_bonus',
            createdAt: now
        };
        // Use batch to ensure atomicity
        const batch = admin.firestore().batch();
        // Update new user's wallet
        const newUserWalletRef = admin.firestore().collection('wallets').doc(userId);
        if (userWalletDoc.exists) {
            batch.update(newUserWalletRef, {
                balance: admin.firestore.FieldValue.increment(referralBonus),
                usedReferral: true,
                history: admin.firestore.FieldValue.arrayUnion(newUserTransaction),
                lastUpdated: now
            });
        }
        else {
            // Create new wallet
            const referralCodeForUser = `user${userId.substring(0, 6)}`;
            batch.set(newUserWalletRef, {
                userId,
                balance: referralBonus,
                referralCode: referralCodeForUser,
                usedReferral: true,
                history: [newUserTransaction],
                grantedBy: 'referral',
                createdAt: now,
                lastUpdated: now
            });
        }
        // Update referrer's wallet
        const referrerWalletRef = admin.firestore().collection('wallets').doc(referralData.userId);
        batch.update(referrerWalletRef, {
            balance: admin.firestore.FieldValue.increment(referralBonus),
            history: admin.firestore.FieldValue.arrayUnion(referrerTransaction),
            lastUpdated: now
        });
        // Update referral code usage
        const referralCodeRef = admin.firestore().collection('referralCodes').doc(referralCode);
        batch.update(referralCodeRef, {
            usedBy: admin.firestore.FieldValue.arrayUnion(userId),
            totalRewards: admin.firestore.FieldValue.increment(referralBonus * 2), // Both users get bonus
            updatedAt: now
        });
        await batch.commit();
        return {
            success: true,
            message: `Referral bonus of $${referralBonus} granted to both users`,
            bonusAmount: referralBonus
        };
    }
    catch (error) {
        console.error('Error in processReferralCode:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Get wallet data for any user (admin only)
exports.getAdminWalletData = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onCall(async (data, context) => {
    var _a;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
        if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
            throw new functions.https.HttpsError('permission-denied', 'Admin access required');
        }
        const { userId } = data;
        if (!userId) {
            throw new functions.https.HttpsError('invalid-argument', 'User ID is required');
        }
        console.log(`Admin ${context.auth.uid} getting wallet data for user ${userId}`);
        // Get wallet from Firestore
        const walletDoc = await admin.firestore().collection('wallets').doc(userId).get();
        if (!walletDoc.exists) {
            // Return empty wallet data if wallet doesn't exist
            return {
                userId,
                balance: 0,
                referralCode: `user${userId.substring(0, 6)}`,
                usedReferral: false,
                history: [],
                grantedBy: 'system',
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            };
        }
        return walletDoc.data();
    }
    catch (error) {
        console.error('Error in getAdminWalletData:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// HTTP endpoint for getting wallet settings (for admin dashboard)
exports.getWalletSettingsHttp = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onRequest(async (req, res) => {
    // Handle CORS
    corsHandler(req, res, async () => {
        var _a;
        try {
            // Only allow POST requests
            if (req.method !== 'POST') {
                res.status(405).json({ error: 'Method not allowed' });
                return;
            }
            // Check authorization header
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            const token = authHeader.split('Bearer ')[1];
            const decodedToken = await admin.auth().verifyIdToken(token);
            const userId = decodedToken.uid;
            // Verify admin role
            const adminDoc = await admin.firestore().collection('users').doc(userId).get();
            if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
                res.status(403).json({ error: 'Unauthorized: Admin access required' });
                return;
            }
            // Get wallet settings
            const settingsDoc = await admin.firestore().collection('adminSettings').doc('walletConfig').get();
            let settings = {
                signupBonus: 0,
                referralBonus: 0,
                enableSignupBonus: false,
                enableReferralBonus: false
            };
            if (settingsDoc.exists) {
                const data = settingsDoc.data();
                settings = {
                    signupBonus: (data === null || data === void 0 ? void 0 : data.signupBonus) || 0,
                    referralBonus: (data === null || data === void 0 ? void 0 : data.referralBonus) || 0,
                    enableSignupBonus: (data === null || data === void 0 ? void 0 : data.enableSignupBonus) || false,
                    enableReferralBonus: (data === null || data === void 0 ? void 0 : data.enableReferralBonus) || false
                };
            }
            res.status(200).json({
                success: true,
                settings
            });
        }
        catch (error) {
            console.error('Error getting wallet settings:', error);
            res.status(500).json({ error: error instanceof Error ? error.message : 'Unknown error' });
        }
    });
});
// HTTP endpoint for configuring wallet settings (for admin dashboard)
exports.configureWalletSettings = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
})
    .https.onRequest(async (req, res) => {
    // Handle CORS
    corsHandler(req, res, async () => {
        var _a;
        try {
            // Only allow POST requests
            if (req.method !== 'POST') {
                res.status(405).json({ error: 'Method not allowed' });
                return;
            }
            // Check authorization header
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                res.status(401).json({ error: 'Unauthorized' });
                return;
            }
            const token = authHeader.split('Bearer ')[1];
            const decodedToken = await admin.auth().verifyIdToken(token);
            const userId = decodedToken.uid;
            // Verify admin role
            const adminDoc = await admin.firestore().collection('users').doc(userId).get();
            if (!adminDoc.exists || ((_a = adminDoc.data()) === null || _a === void 0 ? void 0 : _a.role) !== 'admin') {
                res.status(403).json({ error: 'Unauthorized: Admin access required' });
                return;
            }
            // Get settings from request body
            const { signupBonus, referralBonus, enableSignupBonus, enableReferralBonus } = req.body;
            // Validate settings
            if (typeof signupBonus !== 'number' || typeof referralBonus !== 'number' ||
                typeof enableSignupBonus !== 'boolean' || typeof enableReferralBonus !== 'boolean') {
                res.status(400).json({ error: 'Invalid settings format' });
                return;
            }
            // Save settings
            await admin.firestore().collection('adminSettings').doc('walletConfig').set({
                signupBonus,
                referralBonus,
                enableSignupBonus,
                enableReferralBonus,
                updatedAt: admin.firestore.Timestamp.now(),
                updatedBy: userId
            });
            console.log(`Wallet settings updated by admin: ${userId}`);
            res.status(200).json({
                success: true,
                message: 'Wallet settings updated successfully'
            });
        }
        catch (error) {
            console.error('Error configuring wallet settings:', error);
            res.status(500).json({ error: error instanceof Error ? error.message : 'Unknown error' });
        }
    });
});
// Stripe webhook handler with comprehensive error logging and payment processing
exports.stripeWebhook = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 300,
})
    .https.onRequest(async (req, res) => {
    var _a, _b, _c;
    try {
        console.log('🔔 Stripe webhook received');
        // Only allow POST requests
        if (req.method !== 'POST') {
            console.error('❌ Invalid method:', req.method);
            res.status(405).send('Method not allowed');
            return;
        }
        // Use Stripe (already imported at top)
        // Get Stripe configuration from Firebase Functions config
        const stripeConfig = functions.config();
        const stripeApiKey = ((_a = stripeConfig.stripe) === null || _a === void 0 ? void 0 : _a.api_key) || ((_b = stripeConfig.stripe) === null || _b === void 0 ? void 0 : _b.secret_key);
        const webhookSecret = (_c = stripeConfig.stripe) === null || _c === void 0 ? void 0 : _c.webhook_secret;
        if (!stripeApiKey) {
            console.error('❌ Stripe API key not configured');
            res.status(500).send('Stripe API key not configured');
            return;
        }
        if (!webhookSecret) {
            console.error('❌ Stripe webhook secret not configured');
            res.status(500).send('Stripe webhook secret not configured');
            return;
        }
        const stripe = new Stripe(stripeApiKey, {
            apiVersion: '2025-05-28.basil',
        });
        // Get the signature from headers
        const sig = req.headers['stripe-signature'];
        if (!sig) {
            console.error('❌ No Stripe signature found in headers');
            res.status(400).send('No Stripe signature');
            return;
        }
        console.log('🔐 Verifying webhook signature...');
        let event;
        try {
            // Verify the webhook signature
            event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
            console.log('✅ Webhook signature verified successfully');
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown signature verification error';
            console.error('❌ Webhook signature verification failed:', errorMessage);
            res.status(400).send(`Webhook signature verification failed: ${errorMessage}`);
            return;
        }
        console.log(`📨 Processing webhook event: ${event.type}`);
        // Handle different event types
        switch (event.type) {
            case 'checkout.session.completed':
                await handleCheckoutSessionCompleted(event.data.object);
                break;
            case 'payment_intent.succeeded':
                await handlePaymentIntentSucceeded(event.data.object);
                break;
            case 'account.updated':
                await handleAccountUpdated(event.data.object);
                break;
            default:
                console.log(`ℹ️ Unhandled event type: ${event.type}`);
        }
        console.log('✅ Webhook processed successfully');
        res.status(200).json({ received: true, processed: true });
    }
    catch (error) {
        console.error('❌ Error processing webhook:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown webhook processing error';
        res.status(500).send(`Webhook processing failed: ${errorMessage}`);
    }
});
// Helper function to handle checkout session completed
async function handleCheckoutSessionCompleted(session) {
    try {
        console.log('💳 Processing checkout session completed:', session.id);
        const metadata = session.metadata;
        if (!metadata || !metadata.orderId) {
            console.error('❌ No order ID found in session metadata');
            return;
        }
        const orderId = metadata.orderId;
        console.log(`📦 Processing order: ${orderId}`);
        // Get the order from Firestore
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            console.error(`❌ Order not found: ${orderId}`);
            return;
        }
        const orderData = orderDoc.data();
        console.log(`📋 Order data retrieved for: ${orderId}`);
        // Generate secret code for order completion
        const secretCode = generateSecretCode();
        // Update order with payment completion
        await orderRef.update({
            status: 'payment_completed',
            stripeSessionId: session.id,
            stripePaymentIntentId: session.payment_intent,
            secretCode: secretCode,
            paymentCompletedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        console.log(`✅ Order ${orderId} updated with payment completion`);
        // Update listing status to sold
        if (orderData && orderData.listingId) {
            await admin.firestore().collection('listings').doc(orderData.listingId).update({
                status: 'sold',
                soldAt: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
            });
            console.log(`✅ Listing ${orderData.listingId} marked as sold`);
        }
        // Send notifications
        await sendPaymentNotifications(orderData, orderId, secretCode);
        // Update escrow balance if applicable
        await updateEscrowBalance(orderData);
        console.log(`✅ Checkout session completed processing finished for order: ${orderId}`);
    }
    catch (error) {
        console.error('❌ Error handling checkout session completed:', error);
        throw error;
    }
}
// Helper function to handle payment intent succeeded
async function handlePaymentIntentSucceeded(paymentIntent) {
    try {
        console.log('💰 Processing payment intent succeeded:', paymentIntent.id);
        const metadata = paymentIntent.metadata;
        if (!metadata || !metadata.orderId) {
            console.error('❌ No order ID found in payment intent metadata');
            return;
        }
        const orderId = metadata.orderId;
        console.log(`📦 Processing payment for order: ${orderId}`);
        // Get the order from Firestore
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            console.error(`❌ Order not found: ${orderId}`);
            return;
        }
        const orderData = orderDoc.data();
        // Update order with payment success and start escrow
        const updateData = {
            status: 'in_progress',
            stripePaymentIntentId: paymentIntent.id,
            paymentSucceededAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        // Set delivery confirmation deadline (3 days after delivery)
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.deliveryType) === 'shipping') {
            // For shipping orders, we'll set this when item is delivered
            updateData.shippingTrackingUrl = null;
        }
        else {
            // For in-person orders, seller has 2 days to mark as delivered
            updateData.deliveryDeadline = admin.firestore.Timestamp.fromDate(new Date(Date.now() + 2 * 24 * 60 * 60 * 1000));
        }
        await orderRef.update(updateData);
        // Update listing status to sold
        await admin.firestore().collection('listings').doc(metadata.listingId).update({
            status: 'sold',
            updatedAt: admin.firestore.Timestamp.now()
        });
        console.log(`✅ Order ${orderId} updated with payment success`);
        // Process cashback if applicable
        if (metadata.cashbackAmount && parseFloat(metadata.cashbackAmount) > 0) {
            await processCashback(metadata.buyerId, parseFloat(metadata.cashbackAmount), orderId);
        }
        console.log(`✅ Payment intent succeeded processing finished for order: ${orderId}`);
    }
    catch (error) {
        console.error('❌ Error handling payment intent succeeded:', error);
        throw error;
    }
}
// Helper function to handle account updated
async function handleAccountUpdated(account) {
    try {
        console.log('🏦 Processing account updated:', account.id);
        if (!account.metadata || !account.metadata.userId) {
            console.log('ℹ️ No user ID found in account metadata, skipping');
            return;
        }
        const userId = account.metadata.userId;
        console.log(`👤 Updating connect account for user: ${userId}`);
        // Update the Connect account status in Firestore
        const connectAccountRef = admin.firestore().collection('connectAccounts').doc(userId);
        await connectAccountRef.update({
            isOnboarded: account.charges_enabled && account.payouts_enabled,
            chargesEnabled: account.charges_enabled || false,
            payoutsEnabled: account.payouts_enabled || false,
            detailsSubmitted: account.details_submitted || false,
            updatedAt: admin.firestore.Timestamp.now()
        });
        console.log(`✅ Connect account updated for user: ${userId}`);
        // If account is now fully onboarded, process any pending payouts
        if (account.charges_enabled && account.payouts_enabled) {
            await processPendingPayouts(userId);
        }
    }
    catch (error) {
        console.error('❌ Error handling account updated:', error);
        throw error;
    }
}
// Helper function to generate 6-digit secret code
function generateSecretCode() {
    return Math.floor(100000 + Math.random() * 900000).toString();
}
// Helper function to send payment notifications
async function sendPaymentNotifications(orderData, orderId, secretCode) {
    try {
        console.log(`📧 Sending payment notifications for order: ${orderId}`);
        // Send notification to buyer
        if (orderData.buyerId) {
            await admin.firestore().collection('notifications').add({
                userId: orderData.buyerId,
                type: 'payment_success',
                title: 'Payment Successful!',
                message: `Your payment for "${orderData.listingTitle}" has been processed. Secret code: ${secretCode}`,
                orderId: orderId,
                secretCode: secretCode,
                read: false,
                createdAt: admin.firestore.Timestamp.now()
            });
            console.log(`✅ Buyer notification sent to: ${orderData.buyerId}`);
        }
        // Send notification to seller
        if (orderData.sellerId) {
            await admin.firestore().collection('notifications').add({
                userId: orderData.sellerId,
                type: 'order_received',
                title: 'New Order Received!',
                message: `You have received a new order for "${orderData.listingTitle}". Amount: $${orderData.totalAmount}`,
                orderId: orderId,
                read: false,
                createdAt: admin.firestore.Timestamp.now()
            });
            console.log(`✅ Seller notification sent to: ${orderData.sellerId}`);
        }
        // Send admin notification
        await admin.firestore().collection('adminNotifications').add({
            type: 'payment_completed',
            title: 'Payment Completed',
            message: `Order ${orderId} payment completed. Amount: $${orderData.totalAmount}`,
            orderId: orderId,
            amount: orderData.totalAmount,
            read: false,
            createdAt: admin.firestore.Timestamp.now()
        });
        console.log(`✅ Admin notification sent for order: ${orderId}`);
    }
    catch (error) {
        console.error('❌ Error sending payment notifications:', error);
        throw error;
    }
}
// Helper function to update escrow balance
async function updateEscrowBalance(orderData) {
    try {
        console.log(`💰 Updating escrow balance for order`);
        if (!orderData.sellerId || !orderData.sellerAmount) {
            console.log('ℹ️ No seller amount to escrow');
            return;
        }
        // Create or update escrow record
        const escrowRef = admin.firestore().collection('escrow').doc();
        await escrowRef.set({
            orderId: orderData.id || 'unknown',
            sellerId: orderData.sellerId,
            amount: orderData.sellerAmount,
            status: 'held',
            createdAt: admin.firestore.Timestamp.now(),
            releaseDate: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
            )
        });
        console.log(`✅ Escrow balance updated for seller: ${orderData.sellerId}`);
    }
    catch (error) {
        console.error('❌ Error updating escrow balance:', error);
        throw error;
    }
}
// Helper function to process pending payouts
async function processPendingPayouts(userId) {
    try {
        console.log(`💸 Processing pending payouts for user: ${userId}`);
        // Query pending payouts for this user
        const pendingPayoutsQuery = admin.firestore()
            .collection('pendingPayouts')
            .where('sellerId', '==', userId)
            .where('status', '==', 'pending_delivery_confirmation');
        const pendingPayouts = await pendingPayoutsQuery.get();
        if (pendingPayouts.empty) {
            console.log(`ℹ️ No pending payouts found for user: ${userId}`);
            return;
        }
        // Process each pending payout
        for (const payoutDoc of pendingPayouts.docs) {
            const payoutData = payoutDoc.data();
            // Update payout status to ready for release
            await payoutDoc.ref.update({
                status: 'ready_for_release',
                sellerOnboardedAt: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
            });
            console.log(`✅ Payout ${payoutDoc.id} marked as ready for release`);
        }
        console.log(`✅ Processed ${pendingPayouts.size} pending payouts for user: ${userId}`);
    }
    catch (error) {
        console.error('❌ Error processing pending payouts:', error);
        throw error;
    }
}
// Stripe API functions (matching frontend expectations)
exports.stripeApi = functions
    .runWith({
    memory: '512MB',
    timeoutSeconds: 60,
})
    .https.onRequest(async (req, res) => {
    // Handle CORS for all Stripe API requests
    corsHandler(req, res, async () => {
        try {
            const path = req.path;
            console.log(`🔗 Stripe API request: ${req.method} ${path}`);
            if (path === '/create-checkout-session' && req.method === 'POST') {
                await handleCreateCheckoutSessionWithWallet(req, res);
            }
            else {
                res.status(404).json({ error: 'Endpoint not found' });
            }
        }
        catch (error) {
            console.error('❌ Stripe API error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            res.status(500).json({ error: errorMessage });
        }
    });
});
// Helper function to process cashback for wallet-only orders
async function processCashback(buyerId, cashbackAmount, orderId) {
    if (cashbackAmount <= 0)
        return;
    try {
        console.log(`💰 Processing cashback: $${cashbackAmount} for user: ${buyerId}`);
        const walletRef = admin.firestore().collection('wallets').doc(buyerId);
        const walletDoc = await walletRef.get();
        const transaction = {
            id: admin.firestore().collection('temp').doc().id,
            type: 'credit',
            amount: cashbackAmount,
            description: `Cashback for order ${orderId}`,
            source: 'cashback',
            orderId: orderId,
            createdAt: admin.firestore.Timestamp.now()
        };
        if (!walletDoc.exists) {
            const referralCode = `user${buyerId.substring(0, 6)}`;
            await walletRef.set({
                userId: buyerId,
                balance: cashbackAmount,
                referralCode,
                usedReferral: false,
                history: [transaction],
                grantedBy: 'cashback',
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        else {
            await walletRef.update({
                balance: admin.firestore.FieldValue.increment(cashbackAmount),
                history: admin.firestore.FieldValue.arrayUnion(transaction),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        console.log(`✅ Cashback processed: $${cashbackAmount} for user: ${buyerId}`);
    }
    catch (error) {
        console.error('❌ Error processing cashback:', error);
    }
}
// Enhanced create checkout session with wallet support
async function handleCreateCheckoutSessionWithWallet(req, res) {
    var _a, _b, _c, _d, _e;
    try {
        console.log('💳 Creating checkout session with wallet support...');
        // Check authentication
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({ error: 'Unauthorized: Missing authentication token' });
            return;
        }
        const token = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(token);
        const buyerId = decodedToken.uid;
        // Get request data
        const { listingId, quantity = 1, useWalletBalance = false, orderDetails } = req.body;
        const walletBalanceUsed = (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.appliedWalletCredit) || 0;
        if (!listingId) {
            res.status(400).json({ error: 'Listing ID is required' });
            return;
        }
        console.log(`🛒 Creating checkout for listing: ${listingId}, wallet: $${walletBalanceUsed}`);
        // Get listing details
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            res.status(404).json({ error: 'Listing not found' });
            return;
        }
        const listing = listingDoc.data();
        if (!listing) {
            res.status(404).json({ error: 'Listing data not found' });
            return;
        }
        // Get seller ID
        const sellerId = listing.ownerId || listing.userId;
        if (!sellerId) {
            res.status(400).json({ error: 'Invalid listing data: missing seller information' });
            return;
        }
        // Calculate amounts
        const itemPrice = listing.price;
        const shippingCost = (orderDetails === null || orderDetails === void 0 ? void 0 : orderDetails.shippingFee) || 0;
        const totalBeforeWallet = itemPrice * quantity + shippingCost;
        // Calculate commission rate based on category and price range
        const isTextbookOrCourseMaterial = listing.category === 'textbooks' ||
            listing.category === 'course-materials' ||
            listing.category === 'books';
        // New commission structure with price ranges
        let platformFee;
        const itemTotal = itemPrice * quantity;
        if (itemTotal <= 5) {
            // Flat $0.50 for items $1-$5
            platformFee = 0.50;
        }
        else if (itemTotal <= 10) {
            // 8% for textbooks, 10% for others on $5-$10 range
            const commissionRate = isTextbookOrCourseMaterial ? 0.08 : 0.10;
            platformFee = itemTotal * commissionRate;
        }
        else {
            // 8% for textbooks, 10% for others on $10+ range
            const commissionRate = isTextbookOrCourseMaterial ? 0.08 : 0.10;
            platformFee = itemTotal * commissionRate;
        }
        const sellerAmount = itemTotal - platformFee;
        const cashbackAmount = (itemPrice * quantity) * 0.02;
        console.log(`💰 Commission calculation: Category: ${listing.category}, Platform Fee: $${platformFee}, Seller Gets: $${sellerAmount}`);
        // Validate wallet usage
        let actualWalletUsed = 0;
        if (walletBalanceUsed > 0) {
            const walletDoc = await admin.firestore().collection('wallets').doc(buyerId).get();
            const userWalletBalance = walletDoc.exists ? (((_a = walletDoc.data()) === null || _a === void 0 ? void 0 : _a.balance) || 0) : 0;
            console.log(`💳 Wallet balance: $${userWalletBalance}, requested: $${walletBalanceUsed}`);
            if (walletBalanceUsed > userWalletBalance) {
                res.status(400).json({ error: 'Insufficient wallet balance' });
                return;
            }
            if (walletBalanceUsed > totalBeforeWallet) {
                res.status(400).json({ error: 'Wallet amount cannot exceed total cost' });
                return;
            }
            actualWalletUsed = walletBalanceUsed;
        }
        // Calculate final amount
        const finalAmount = Math.max(0, totalBeforeWallet - actualWalletUsed);
        console.log(`🧮 Final calculation: Total: $${totalBeforeWallet} - Wallet: $${actualWalletUsed} = Stripe: $${finalAmount}`);
        console.log(`💼 Seller payout: Item: $${itemPrice * quantity} - Platform Fee: $${platformFee} = Seller Gets: $${sellerAmount}`);
        console.log(`🏦 Platform absorbs wallet credit: $${actualWalletUsed} (seller still gets full $${sellerAmount})`);
        // Generate 6-digit secret code
        const secretCode = Math.floor(100000 + Math.random() * 900000).toString();
        // Create order
        const orderRef = admin.firestore().collection('orders').doc();
        const orderId = orderRef.id;
        const orderData = {
            id: orderId,
            listingId,
            listingTitle: listing.title,
            listingCategory: listing.category,
            buyerId,
            sellerId,
            quantity,
            itemPrice,
            shippingCost,
            totalBeforeWallet,
            walletAmountUsed: actualWalletUsed,
            finalStripeAmount: finalAmount,
            platformFee: platformFee,
            commissionRate: (itemTotal <= 5) ? 'flat_fee' : (isTextbookOrCourseMaterial ? '8%' : '10%'),
            sellerAmount: sellerAmount,
            commissionAmount: platformFee, // Keep for backward compatibility
            cashbackAmount,
            secretCode,
            deliveryType: listing.deliveryMethod || 'in_person',
            status: finalAmount === 0 ? 'in_progress' : 'pending_payment',
            returnEligibleUntil: null, // Set after delivery
            releasedToSeller: false,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        // Use transaction for atomicity
        await admin.firestore().runTransaction(async (transaction) => {
            transaction.set(orderRef, orderData);
            if (actualWalletUsed > 0) {
                const walletRef = admin.firestore().collection('wallets').doc(buyerId);
                const walletTransaction = {
                    id: admin.firestore().collection('temp').doc().id,
                    type: 'debit',
                    amount: actualWalletUsed,
                    description: `Purchase: ${listing.title}`,
                    source: 'purchase_deduction',
                    orderId,
                    createdAt: admin.firestore.Timestamp.now()
                };
                transaction.update(walletRef, {
                    balance: admin.firestore.FieldValue.increment(-actualWalletUsed),
                    history: admin.firestore.FieldValue.arrayUnion(walletTransaction),
                    lastUpdated: admin.firestore.Timestamp.now()
                });
            }
        });
        // Handle zero-amount orders
        if (finalAmount === 0) {
            console.log(`🎉 Order fully covered by wallet balance!`);
            res.status(200).json({
                success: true,
                paidWithWallet: true,
                walletAmountUsed: actualWalletUsed,
                orderId,
                message: `Order paid successfully using $${actualWalletUsed} from wallet balance`
            });
            return;
        }
        // Create Stripe session for remaining amount
        const stripeConfig = functions.config();
        const stripeApiKey = ((_b = stripeConfig.stripe) === null || _b === void 0 ? void 0 : _b.api_key) || ((_c = stripeConfig.stripe) === null || _c === void 0 ? void 0 : _c.secret_key);
        if (!stripeApiKey) {
            res.status(500).json({ error: 'Stripe API key not configured' });
            return;
        }
        const stripe = new Stripe(stripeApiKey, { apiVersion: '2025-05-28.basil' });
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [{
                    price_data: {
                        currency: 'usd',
                        product_data: {
                            name: `${listing.title}${actualWalletUsed > 0 ? ` (after $${actualWalletUsed} wallet credit)` : ''}`,
                            description: listing.description || 'Hive Campus item',
                        },
                        unit_amount: Math.round(finalAmount * 100),
                    },
                    quantity: 1,
                }],
            mode: 'payment',
            success_url: `${((_d = stripeConfig.app) === null || _d === void 0 ? void 0 : _d.url) || 'https://h1c1-798a8.web.app'}/order-success?session_id={CHECKOUT_SESSION_ID}&order_id=${orderId}`,
            cancel_url: `${((_e = stripeConfig.app) === null || _e === void 0 ? void 0 : _e.url) || 'https://h1c1-798a8.web.app'}/listing/${listingId}`,
            metadata: {
                orderId,
                listingId,
                buyerId,
                sellerId,
                walletAmountUsed: actualWalletUsed.toString(),
                originalTotal: totalBeforeWallet.toString(),
                finalAmount: finalAmount.toString(),
                platformFee: platformFee.toString(),
                sellerAmount: sellerAmount.toString(),
                commissionRate: (itemTotal <= 5) ? 'flat_fee' : (isTextbookOrCourseMaterial ? '8%' : '10%'),
                listingCategory: listing.category || 'general',
            },
        });
        // Update order with session ID
        await orderRef.update({
            stripeSessionId: session.id,
            updatedAt: admin.firestore.Timestamp.now()
        });
        res.status(200).json({
            success: true,
            sessionId: session.id,
            sessionUrl: session.url,
            orderId,
            walletAmountUsed: actualWalletUsed,
            finalStripeAmount: finalAmount,
            originalTotal: totalBeforeWallet
        });
        console.log(`✅ Checkout session created: ${session.id} for $${finalAmount}`);
    }
    catch (error) {
        console.error('❌ Error creating checkout session:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.status(500).json({ error: errorMessage });
    }
}
// Create Stripe checkout session for testing
exports.createCheckoutSession = functions
    .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
})
    .https.onCall(async (data, context) => {
    var _a, _b, _c, _d;
    try {
        // Check authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { listingId, quantity = 1 } = data;
        const buyerId = context.auth.uid;
        if (!listingId) {
            throw new functions.https.HttpsError('invalid-argument', 'Listing ID is required');
        }
        console.log(`🛒 Creating checkout session for listing: ${listingId}, buyer: ${buyerId}`);
        // Get listing details
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Listing not found');
        }
        const listing = listingDoc.data();
        if (!listing) {
            throw new functions.https.HttpsError('not-found', 'Listing data not found');
        }
        // Calculate amounts
        const itemPrice = listing.price;
        const subtotal = itemPrice * quantity;
        const commissionRate = 0.05; // 5% commission
        const commissionAmount = subtotal * commissionRate;
        const sellerAmount = subtotal - commissionAmount;
        const cashbackRate = 0.02; // 2% cashback
        const cashbackAmount = subtotal * cashbackRate;
        // Create order in Firestore first
        const orderRef = admin.firestore().collection('orders').doc();
        const orderId = orderRef.id;
        // Get seller ID from listing (handle both ownerId and userId fields)
        const sellerId = listing.ownerId || listing.userId;
        console.log('🔍 Callable function - Checking sellerId:', { ownerId: listing.ownerId, userId: listing.userId, sellerId });
        if (!sellerId || sellerId === undefined || sellerId === null || sellerId === '') {
            console.error('❌ No seller ID found in listing data:', {
                ownerId: listing.ownerId,
                userId: listing.userId,
                sellerId: sellerId,
                listingKeys: Object.keys(listing)
            });
            throw new functions.https.HttpsError('invalid-argument', 'Invalid listing data: missing seller information');
        }
        console.log(`👤 Seller ID: ${sellerId}`);
        // Validate all required fields before creating order
        const orderData = {
            id: orderId,
            listingId: listingId,
            listingTitle: listing.title,
            buyerId: buyerId,
            sellerId: sellerId,
            quantity: quantity,
            itemPrice: itemPrice,
            subtotal: subtotal,
            commissionAmount: commissionAmount,
            sellerAmount: sellerAmount,
            cashbackAmount: cashbackAmount,
            totalAmount: subtotal,
            status: 'pending_payment',
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        };
        // Double-check that sellerId is not undefined before saving
        if (!orderData.sellerId || orderData.sellerId === undefined) {
            console.error('❌ sellerId is undefined in order data:', orderData);
            throw new functions.https.HttpsError('invalid-argument', 'Cannot create order: seller ID is undefined');
        }
        console.log('💾 Callable function - Creating order with data:', JSON.stringify(orderData, null, 2));
        await orderRef.set(orderData);
        console.log(`📦 Order created: ${orderId}`);
        // Use Stripe (already imported at top)
        const stripeConfig = functions.config();
        const stripeApiKey = ((_a = stripeConfig.stripe) === null || _a === void 0 ? void 0 : _a.api_key) || ((_b = stripeConfig.stripe) === null || _b === void 0 ? void 0 : _b.secret_key);
        if (!stripeApiKey) {
            throw new functions.https.HttpsError('internal', 'Stripe API key not configured');
        }
        const stripe = new Stripe(stripeApiKey, {
            apiVersion: '2025-05-28.basil',
        });
        // Create Stripe checkout session
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [
                {
                    price_data: {
                        currency: 'usd',
                        product_data: {
                            name: listing.title,
                            description: listing.description || 'Hive Campus item',
                        },
                        unit_amount: Math.round(itemPrice * 100), // Convert to cents
                    },
                    quantity: quantity,
                },
            ],
            mode: 'payment',
            success_url: `${((_c = stripeConfig.app) === null || _c === void 0 ? void 0 : _c.url) || 'https://h1c1-798a8.web.app'}/order-success?session_id={CHECKOUT_SESSION_ID}&order_id=${orderId}`,
            cancel_url: `${((_d = stripeConfig.app) === null || _d === void 0 ? void 0 : _d.url) || 'https://h1c1-798a8.web.app'}/listing/${listingId}`,
            metadata: {
                orderId: orderId,
                listingId: listingId,
                buyerId: buyerId,
                sellerId: sellerId,
                cashbackAmount: cashbackAmount.toString(),
            },
        });
        console.log(`✅ Stripe checkout session created: ${session.id}`);
        // Update order with session ID
        await orderRef.update({
            stripeSessionId: session.id,
            updatedAt: admin.firestore.Timestamp.now()
        });
        return {
            sessionId: session.id,
            sessionUrl: session.url,
            orderId: orderId
        };
    }
    catch (error) {
        console.error('❌ Error creating checkout session:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        throw new functions.https.HttpsError('internal', errorMessage);
    }
});
// Secret code release function
exports.releaseEscrowWithCode = functions.https.onCall(async (data, context) => {
    try {
        // Verify authentication
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { orderId, secretCode } = data;
        const buyerId = context.auth.uid;
        if (!orderId || !secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code are required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify buyer
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) !== buyerId) {
            throw new functions.https.HttpsError('permission-denied', 'Only the buyer can release funds');
        }
        // Verify secret code
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.secretCode) !== secretCode) {
            throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
        }
        // Check if already completed
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.status) === 'completed' || (orderData === null || orderData === void 0 ? void 0 : orderData.releasedToSeller)) {
            throw new functions.https.HttpsError('failed-precondition', 'Funds already released');
        }
        // Release funds to seller
        await releaseFundsToSeller(orderData, orderId);
        return { success: true, message: 'Funds released successfully' };
    }
    catch (error) {
        console.error('Error releasing escrow with code:', error);
        throw error;
    }
});
// Auto-release escrow after 3 days
exports.autoReleaseEscrow = functions.pubsub.schedule('every 1 hours').onRun(async () => {
    try {
        const now = admin.firestore.Timestamp.now();
        const threeDaysAgo = admin.firestore.Timestamp.fromDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000));
        // Find orders eligible for auto-release
        const ordersQuery = await admin.firestore()
            .collection('orders')
            .where('status', '==', 'delivered')
            .where('deliveryConfirmedAt', '<=', threeDaysAgo)
            .where('releasedToSeller', '==', false)
            .get();
        const promises = [];
        ordersQuery.docs.forEach((doc) => {
            const orderData = doc.data();
            promises.push(releaseFundsToSeller(orderData, doc.id));
        });
        await Promise.all(promises);
        console.log(`Auto-released ${ordersQuery.docs.length} orders`);
    }
    catch (error) {
        console.error('Error in auto-release escrow:', error);
    }
});
// Helper function to release funds to seller
async function releaseFundsToSeller(orderData, orderId) {
    try {
        // Update order status
        await admin.firestore().collection('orders').doc(orderId).update({
            status: 'completed',
            releasedToSeller: true,
            fundsReleasedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Add funds to seller's wallet (simplified - in production you'd use Stripe transfers)
        const sellerWalletRef = admin.firestore().collection('wallets').doc(orderData.sellerId);
        const sellerWalletDoc = await sellerWalletRef.get();
        const transaction = {
            id: admin.firestore().collection('temp').doc().id,
            type: 'credit',
            amount: orderData.sellerAmount,
            description: `Sale: ${orderData.listingTitle}`,
            orderId: orderId,
            createdAt: admin.firestore.Timestamp.now()
        };
        if (sellerWalletDoc.exists) {
            await sellerWalletRef.update({
                balance: admin.firestore.FieldValue.increment(orderData.sellerAmount),
                history: admin.firestore.FieldValue.arrayUnion(transaction),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        else {
            await sellerWalletRef.set({
                userId: orderData.sellerId,
                balance: orderData.sellerAmount,
                history: [transaction],
                createdAt: admin.firestore.Timestamp.now(),
                lastUpdated: admin.firestore.Timestamp.now()
            });
        }
        console.log(`✅ Funds released to seller: ${orderData.sellerId}, amount: $${orderData.sellerAmount}`);
    }
    catch (error) {
        console.error('Error releasing funds to seller:', error);
        throw error;
    }
}
// Mark in-person delivery as completed
exports.markDeliveryCompleted = functions.https.onCall(async (data, context) => {
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { orderId } = data;
        const sellerId = context.auth.uid;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify seller
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.sellerId) !== sellerId) {
            throw new functions.https.HttpsError('permission-denied', 'Only the seller can mark delivery as completed');
        }
        // Verify it's an in-person delivery
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.deliveryType) !== 'in_person') {
            throw new functions.https.HttpsError('invalid-argument', 'This function is only for in-person deliveries');
        }
        // Verify order is in correct status
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.status) !== 'in_progress') {
            throw new functions.https.HttpsError('failed-precondition', 'Order must be in progress to mark as delivered');
        }
        // Mark as delivered and set return window
        const deliveryDate = new Date();
        const returnDeadline = new Date(deliveryDate.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days
        await orderRef.update({
            status: 'delivered',
            deliveryConfirmedAt: admin.firestore.Timestamp.fromDate(deliveryDate),
            returnEligibleUntil: admin.firestore.Timestamp.fromDate(returnDeadline),
            updatedAt: admin.firestore.Timestamp.now()
        });
        return { success: true, message: 'Delivery marked as completed' };
    }
    catch (error) {
        console.error('Error marking delivery completed:', error);
        throw error;
    }
});
// Request return
exports.requestReturn = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        if (!context.auth) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { orderId, reason } = data;
        const buyerId = context.auth.uid;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        // Get order
        const orderRef = admin.firestore().collection('orders').doc(orderId);
        const orderDoc = await orderRef.get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        // Verify buyer
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.buyerId) !== buyerId) {
            throw new functions.https.HttpsError('permission-denied', 'Only the buyer can request returns');
        }
        // Check if return window is still open
        const returnDeadline = (_a = orderData === null || orderData === void 0 ? void 0 : orderData.returnEligibleUntil) === null || _a === void 0 ? void 0 : _a.toDate();
        const now = new Date();
        if (!returnDeadline || now > returnDeadline) {
            throw new functions.https.HttpsError('failed-precondition', 'Return window has expired');
        }
        // Update order with return request
        await orderRef.update({
            status: 'return_requested',
            returnRequested: true,
            returnReason: reason || 'No reason provided',
            returnRequestedAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now()
        });
        return { success: true, message: 'Return request submitted' };
    }
    catch (error) {
        console.error('Error requesting return:', error);
        throw error;
    }
});
